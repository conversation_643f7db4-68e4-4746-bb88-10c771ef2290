<template>
    <div class="font-14 color-two-grey">
        <el-row>
            <el-col :span="24" class="all-padding-16 border blue-bg"
            >证书概况</el-col>
        </el-row>
        <el-row>
            <el-col :span="6" class="all-padding-16 border blue-bg"
            >初次获证日期</el-col>
            <el-col :span="6" class="all-padding-16 border white-bg"
            >{{certificanteGeneralInfo?.InitCertDate.split('T')[0] || '-'}}</el-col>
            <el-col :span="6" class="all-padding-16 border blue-bg"
            >最近获证日期</el-col>
            <el-col :span="6" class="all-padding-16 border white-bg"
            >{{certificanteGeneralInfo?.LastCertDate.split('T')[0] || '-'}}</el-col>
        </el-row>
        <el-row>
            <el-col :span="6" class="all-padding-16 border blue-bg"
            >本年度获证数量</el-col>
            <el-col :span="6" class="all-padding-16 border white-bg"
            >{{certificanteGeneralInfo?.CertCountYear}}</el-col>
            <el-col :span="6" class="all-padding-16 border blue-bg"
            >涵盖证书类别</el-col>
            <el-col :span="6" class="all-padding-16 border white-bg"
            >
                <el-tooltip :content='text'>
                    <div class="text-ellipsis text-nowrap">{{text}}</div>
                </el-tooltip>
            </el-col>
        </el-row>
    </div>
</template>

<script lang='ts' setup>
import { onMounted, watch, ref } from 'vue'
import type { CertificateGeneralItem } from '@/types/model'

const props = defineProps<{
    row:CertificateGeneralItem
}>()
const text = ref('')
const certificanteGeneralInfo = ref<CertificateGeneralItem>()
watch(() => props.row, (val) => {
    console.log('props.row', val)
    if(val){
        certificanteGeneralInfo.value = val
        text.value =`${val.CertL2Count}种（${val.CertL2Type.join('、')}）` 
    }
})
onMounted(() => {
})
</script>

<style lang='scss' scoped>
.white-bg {
    background-color: #ffffff;
}
.blue-bg{
    background-color: #EEF5FE;
}
</style>